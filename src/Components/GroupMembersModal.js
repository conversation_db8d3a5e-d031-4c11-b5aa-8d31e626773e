import React, { useRef, useEffect } from 'react';
import {
  Modal,
  View,
  TouchableOpacity,
  StyleSheet,
  Platform,
  Animated,
  ScrollView,
  Dimensions,
} from 'react-native';
import { colors } from '../Custom/Colors';
import { hp, wp } from '../Custom/Responsiveness';
import ResponsiveText from '../Custom/RnText';
import Icon from '../Custom/Icon';
import { globalpath } from '../Custom/globalpath';
import useTheme from '../Redux/useTheme';
import * as Animatable from 'react-native-animatable';

const { width, height } = Dimensions.get('window');

const GroupMembersModal = ({ visible, onClose }) => {
  const { themeColor, backgroundColor, getTextColor, getSecondaryTextColor } = useTheme();
  const slideAnim = useRef(new Animated.Value(height)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;

  // Static group members data with attractive avatars
  const groupMembers = [
    {
      id: 1,
      name: '<PERSON><PERSON>',
      role: 'Team Lead',
      status: 'online',
      avatar: { backgroundColor: '#FF6B6B', initial: 'A' },
      lastSeen: 'Active now',
    },
    {
      id: 2,
      name: 'Hassan Farooq',
      role: 'Developer',
      status: 'online',
      avatar: { backgroundColor: '#4ECDC4', initial: 'H' },
      lastSeen: 'Active now',
    },
    {
      id: 3,
      name: 'Haris Khan',
      role: 'Designer',
      status: 'offline',
      avatar: { backgroundColor: '#45B7D1', initial: 'H' },
      lastSeen: '2 hours ago',
    },
    {
      id: 4,
      name: 'Sarah Ahmed',
      role: 'Manager',
      status: 'online',
      avatar: { backgroundColor: '#96CEB4', initial: 'S' },
      lastSeen: 'Active now',
    },
    {
      id: 5,
      name: 'Ali Raza',
      role: 'Analyst',
      status: 'offline',
      avatar: { backgroundColor: '#FFEAA7', initial: 'A' },
      lastSeen: '1 day ago',
    },
  ];

  useEffect(() => {
    if (visible) {
      Animated.parallel([
        Animated.spring(slideAnim, {
          toValue: 0,
          useNativeDriver: true,
          tension: 50,
          friction: 8,
        }),
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          useNativeDriver: true,
          tension: 50,
          friction: 8,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: height,
          duration: 250,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 250,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 0.8,
          duration: 250,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [visible]);

  const renderMemberCard = (member, index) => (
    <Animatable.View
      key={member.id}
      animation="fadeInUp"
      delay={index * 100}
      duration={600}
      style={[styles.memberCard, { backgroundColor }]}
    >
      {/* Glassmorphism effect overlay */}
      <View style={[styles.glassmorphismOverlay, { backgroundColor: `${themeColor}08` }]} />

      <View style={styles.memberContent}>
        {/* Avatar with status indicator */}
        <View style={styles.avatarContainer}>
          <View style={[styles.avatar, { backgroundColor: member.avatar.backgroundColor }]}>
            <ResponsiveText color={colors.white} size={5} weight="600">
              {member.avatar.initial}
            </ResponsiveText>
          </View>

          {/* Status indicator */}
          <View style={[
            styles.statusIndicator,
            { backgroundColor: member.status === 'online' ? '#4CAF50' : colors.grey }
          ]} />
        </View>

        {/* Member info */}
        <View style={styles.memberInfo}>
          <ResponsiveText color={getTextColor()} size={4.2} weight="600" numberOfLines={1}>
            {member.name}
          </ResponsiveText>
          <ResponsiveText color={getSecondaryTextColor()} size={3.2} style={{ opacity: 0.8 }}>
            {member.role}
          </ResponsiveText>
          <ResponsiveText color={getSecondaryTextColor()} size={2.8} style={{ opacity: 0.6 }}>
            {member.lastSeen}
          </ResponsiveText>
        </View>

        {/* Action button */}
        <TouchableOpacity style={[styles.actionButton, { backgroundColor: `${themeColor}15` }]}>
          <Icon
            source={globalpath.user}
            size={wp(4)}
            tintColor={themeColor}
          />
        </TouchableOpacity>
      </View>
    </Animatable.View>
  );

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      statusBarTranslucent
      onRequestClose={onClose}
    >
      <Animated.View
        style={[
          styles.modalBackdrop,
          {
            opacity: fadeAnim,
          },
        ]}
      >
        <TouchableOpacity
          style={styles.touchableOutside}
          activeOpacity={1}
          onPress={onClose}
        />

        <Animated.View
          style={[
            styles.modalContainer,
            { backgroundColor },
            {
              transform: [
                { translateY: slideAnim },
                { scale: scaleAnim },
              ],
            },
          ]}
        >
          {/* Header */}
          <View style={styles.modalHeader}>
            <View style={styles.titleContainer}>
              <View style={[styles.titleIconContainer, { backgroundColor: `${themeColor}15` }]}>
                <Icon
                  source={globalpath.users}
                  size={wp(5)}
                  tintColor={themeColor}
                />
              </View>
              <View>
                <ResponsiveText color={getTextColor()} weight="700" size={5.2}>
                  Group Members
                </ResponsiveText>
                <ResponsiveText color={getSecondaryTextColor()} size={3.2} style={{ opacity: 0.7 }}>
                  {groupMembers.length} members
                </ResponsiveText>
              </View>
            </View>

            <TouchableOpacity
              onPress={onClose}
              style={[styles.closeButton, { backgroundColor: colors.lightGrey6 }]}
              activeOpacity={0.7}
            >
              <Icon
                source={globalpath.cross}
                size={wp(4.5)}
                tintColor={colors.greyBlack}
              />
            </TouchableOpacity>
          </View>

          {/* Members list */}
          <ScrollView
            style={styles.membersContainer}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.membersContent}
          >
            {groupMembers.map((member, index) => renderMemberCard(member, index))}
          </ScrollView>
        </Animated.View>
      </Animated.View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalBackdrop: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.6)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: wp(4),
  },
  touchableOutside: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  modalContainer: {
    width: '100%',
    maxHeight: hp(80),
    borderRadius: wp(6),
    paddingTop: hp(3),
    paddingHorizontal: wp(5),
    paddingBottom: Platform.select({
      ios: hp(4),
      android: hp(3),
    }),
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 10,
    },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    elevation: 15,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: hp(3),
    paddingHorizontal: wp(1),
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  titleIconContainer: {
    width: wp(12),
    height: wp(12),
    borderRadius: wp(6),
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: wp(3),
  },
  closeButton: {
    padding: wp(2.5),
    borderRadius: wp(6),
  },
  membersContainer: {
    flex: 1,
  },
  membersContent: {
    paddingBottom: hp(2),
  },
  memberCard: {
    marginBottom: hp(2),
    borderRadius: wp(4),
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  glassmorphismOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderRadius: wp(4),
  },
  memberContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: wp(4),
    position: 'relative',
    zIndex: 1,
  },
  avatarContainer: {
    position: 'relative',
    marginRight: wp(4),
  },
  avatar: {
    width: wp(14),
    height: wp(14),
    borderRadius: wp(7),
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  statusIndicator: {
    position: 'absolute',
    bottom: wp(0.5),
    right: wp(0.5),
    width: wp(4),
    height: wp(4),
    borderRadius: wp(2),
    borderWidth: 2,
    borderColor: colors.white,
  },
  memberInfo: {
    flex: 1,
    justifyContent: 'center',
  },
  actionButton: {
    width: wp(10),
    height: wp(10),
    borderRadius: wp(5),
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default GroupMembersModal;
