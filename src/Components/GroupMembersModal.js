import React, { useRef, useEffect } from 'react';
import {
  Modal,
  View,
  TouchableOpacity,
  StyleSheet,
  Platform,
  Animated,
  ScrollView,
  Dimensions,
} from 'react-native';
import { colors } from '../Custom/Colors';
import { hp, wp } from '../Custom/Responsiveness';
import ResponsiveText from '../Custom/RnText';
import Icon from '../Custom/Icon';
import { globalpath } from '../Custom/globalpath';
import useTheme from '../Redux/useTheme';
import * as Animatable from 'react-native-animatable';

const { width, height } = Dimensions.get('window');

const GroupMembersModal = ({ visible, onClose }) => {
  const { themeColor, backgroundColor, getTextColor, getSecondaryTextColor } = useTheme();
  const slideAnim = useRef(new Animated.Value(hp(70))).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;

  // Static group members data - simple name and avatar only
  const groupMembers = [
    {
      id: 1,
      name: '<PERSON><PERSON>',
      avatarColor: '#FF6B6B',
    },
    {
      id: 2,
      name: '<PERSON>',
      avatarColor: '#4ECDC4',
    },
    {
      id: 3,
      name: 'Haris Khan',
      avatarColor: '#45B7D1',
    },
    {
      id: 4,
      name: 'Sarah Ahmed',
      avatarColor: '#96CEB4',
    },
    {
      id: 5,
      name: '<PERSON> Raza',
      avatarColor: '#FFEAA7',
    },
  ];

  // Function to get initials from name
  const getInitials = (name) => {
    return name.split(' ').map(word => word.charAt(0)).join('').toUpperCase();
  };

  useEffect(() => {
    if (visible) {
      Animated.parallel([
        Animated.spring(slideAnim, {
          toValue: 0,
          useNativeDriver: true,
          tension: 50,
          friction: 8,
        }),
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: hp(70),
          duration: 250,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 250,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [visible]);

  const renderMemberCard = (member, index) => (
    <Animatable.View
      key={member.id}
      animation="fadeInUp"
      delay={index * 80}
      duration={500}
      style={styles.memberCard}
    >
      {/* Avatar */}
      <View style={[styles.avatar, { backgroundColor: member.avatarColor }]}>
        <ResponsiveText color={colors.white} size={4.5} weight="700">
          {getInitials(member.name)}
        </ResponsiveText>
      </View>

      {/* Name */}
      <ResponsiveText
        color={getTextColor()}
        size={3.8}
        weight="600"
        numberOfLines={1}
        style={styles.memberName}
      >
        {member.name}
      </ResponsiveText>
    </Animatable.View>
  );

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      statusBarTranslucent
      onRequestClose={onClose}
    >
      <Animated.View
        style={[
          styles.modalBackdrop,
          {
            opacity: fadeAnim,
          },
        ]}
      >
        <TouchableOpacity
          style={styles.touchableOutside}
          activeOpacity={1}
          onPress={onClose}
        />

        <Animated.View
          style={[
            styles.modalContainer,
            { backgroundColor },
            {
              transform: [{ translateY: slideAnim }],
            },
          ]}
        >
          {/* Handle bar */}
          <View style={styles.handleBar} />

          {/* Header */}
          <View style={styles.modalHeader}>
            <ResponsiveText color={getTextColor()} weight="700" size={5}>
              Group Members
            </ResponsiveText>

            <TouchableOpacity
              onPress={onClose}
              style={[styles.closeButton, { backgroundColor: colors.lightGrey6 }]}
              activeOpacity={0.7}
            >
              <Icon
                source={globalpath.cross}
                size={wp(4.5)}
                tintColor={colors.greyBlack}
              />
            </TouchableOpacity>
          </View>

          {/* Members grid */}
          <ScrollView
            style={styles.membersContainer}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.membersContent}
          >
            <View style={styles.membersGrid}>
              {groupMembers.map((member, index) => renderMemberCard(member, index))}
            </View>
          </ScrollView>
        </Animated.View>
      </Animated.View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalBackdrop: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.6)',
    justifyContent: 'flex-end',
  },
  touchableOutside: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  modalContainer: {
    width: '100%',
    height: hp(70),
    borderTopLeftRadius: wp(6),
    borderTopRightRadius: wp(6),
    paddingTop: hp(3),
    paddingHorizontal: wp(5),
    paddingBottom: Platform.select({
      ios: hp(4),
      android: hp(3),
    }),
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -10,
    },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    elevation: 15,
  },
  handleBar: {
    width: wp(12),
    height: hp(0.6),
    backgroundColor: colors.lightGrey5,
    borderRadius: wp(3),
    alignSelf: 'center',
    marginBottom: hp(2),
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: hp(3),
    paddingHorizontal: wp(1),
  },
  closeButton: {
    padding: wp(2.5),
    borderRadius: wp(6),
  },
  membersContainer: {
    flex: 1,
  },
  membersContent: {
    paddingBottom: hp(2),
  },
  membersGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingHorizontal: wp(2),
  },
  memberCard: {
    width: wp(25),
    alignItems: 'center',
    marginBottom: hp(3),
    paddingVertical: hp(1),
  },
  avatar: {
    width: wp(16),
    height: wp(16),
    borderRadius: wp(8),
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: hp(1),
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 5,
  },
  memberName: {
    textAlign: 'center',
    marginTop: hp(0.5),
  },
});

export default GroupMembersModal;
